import express, { Application } from 'express';
import router from './routes/routes.js';
import helmet from 'helmet';

export const createApp = (): Application => {
  // Initialize Express application
  const app = express();

  // Global middleware
  app.use(helmet()); // Set security-related HTTP headers
  app.use(express.json()); // Parse incoming JSON requests into req.body
  app.disable('x-powered-by'); // Hide "X-Powered-By" header to avoid exposing server details

  // Register routes
  app.use('/api', router);

  return app;
};
