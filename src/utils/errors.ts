// Base class for custom application errors with HTTP status codes
class AppError extends Error {
  public readonly statusCode: number;

  constructor(message: string, statusCode: number) {
    super(message); // Initialize built-in Error
    this.statusCode = statusCode; // Set HTTP status code
    Object.setPrototypeOf(this, new.target.prototype); // Fix prototype chain
  }
}

// 500 Internal Server Error
class InternalServerError extends AppError {
  constructor(message = 'Internal Server Error') {
    super(message, 500);
  }
}

export { AppError, InternalServerError };
