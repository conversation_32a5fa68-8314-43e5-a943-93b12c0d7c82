#!/bin/bash
set -e

# standard test helper script.

#  1. TODO: Runs unit tests and end-to-end tests
echo "Running unit and end-to end tests"
npm test

echo
#  2. Runs SAST scan on the codebase
CURRENT_DIR=$(pwd)

if [ -f "$CURRENT_DIR/bin/bearer" ]; then
    echo "Running SAST (Software Application Security Testing)"
    "$CURRENT_DIR/bin/bearer" scan .
else
    # install bearer cli
    curl -sfL https://raw.githubusercontent.com/Bearer/bearer/main/contrib/install.sh | sh

    # Run the downloaded program
    "$CURRENT_DIR/bin/bearer" scan .
fi
