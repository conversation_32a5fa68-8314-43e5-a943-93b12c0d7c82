import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import 'dotenv/config';
import { AppConfig } from '../src/types/config.js';

// Paths to configuration files
const CONFIG_PATH = path.join(process.cwd(), 'config.yaml');

/**
 * @description Helper function to get a required environment variable.
 * The application will exit if the variable is not set.
 * @param {string} key - The environment variable key.
 * @returns {string} The value of the environment variable.
 */
const getRequiredEnv = (key: string): string => {
  const value = process.env[key];
  if (!value) {
    console.error(`❌ Required environment variable ${key} is not set. The application will exit.`);
    process.exit(1);
  }
  return value;
};
// Read and parse the YAML configuration file
let yamlConfig: any;
try {
  const fileContents = fs.readFileSync(CONFIG_PATH, 'utf-8');
  yamlConfig = yaml.load(fileContents);
} catch (error) {
  throw new Error(`Failed to read config.yaml: ${error}. Please run the setup script first.`);
}

// Build configuration object combining YAML structure with .env sensitive data
const CONFIG: AppConfig = {
  MainDatabase: {
    host: getRequiredEnv('DB_HOST') || yamlConfig.MainDatabase?.host,
    port: parseInt(getRequiredEnv('DB_PORT'), 10),
    name: getRequiredEnv('DB_NAME') || yamlConfig.MainDatabase?.name,
  },
  DatabaseOwner: {
    user: getRequiredEnv('DB_OWNER_USER') || yamlConfig.DatabaseOwner?.user,
    password: getRequiredEnv('DB_OWNER_PASSWORD'),
  },
  PostgresSuperuser: {
    user: getRequiredEnv('DB_POSTGRES_USER') || yamlConfig.PostgresSuperuser?.user,
    password: getRequiredEnv('DB_POSTGRES_PASSWORD'),
  },
  ApplicationUser: {
    user: getRequiredEnv('DB_APP_USER') || yamlConfig.ApplicationUser?.user,
    password: getRequiredEnv('DB_APP_PASSWORD'),
  },
  MigrationUser: {
    user: getRequiredEnv('DB_MIGRATION_USER') || yamlConfig.MigrationUser?.user,
    password: getRequiredEnv('DB_MIGRATION_PASSWORD'),
  },
  Server: {
    port: parseInt(getRequiredEnv('SERVER_PORT'), 10),
    host: getRequiredEnv('SERVER_HOST') || yamlConfig.Server?.host,
  },
};

export { CONFIG, CONFIG_PATH };
