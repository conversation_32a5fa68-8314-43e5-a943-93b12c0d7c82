# Rishiflow Project Testing

### Software Application Security Testing

To run SAST scan on the codebase

```shell
bash script/tests.sh
```

The script scans the whole codebase for known vulnerabilities and how to fix them.

### Unit and End to End Testing

To run unit tests and e2e test you may choose to run testing script which runs the whole testing scripts or use npm after running the setup script.

All the test command and script will display the test coverage in a table format

1. **using npm**

```bash
# Using npm
npm test
```

2. using test script

```bash
bash script/tests.sh
```
