// Import the type definition for Jest config (helps TypeScript catch mistakes)
import type { Config } from 'jest';

// Define the Jest configuration object
const config: Config = {
  // Use the ESM preset for ts-jest
  // This preset allows <PERSON><PERSON> to work with ES Modules (import/export) in TypeScript
  preset: 'ts-jest/presets/default-esm',

  // Set the test environment to Node.js
  // This is important for backend tests (Express, Supertest, etc.)
  testEnvironment: 'node',

  // Global configuration for ts-jest
  globals: {
    'ts-jest': {
      // Use ESM module compilation
      // Ensures ts-jest transpiles TypeScript files using ES Module syntax
      useESM: true
    }
  },

  // File extensions Jest will look for when resolving modules
  // Includes TypeScript, JavaScript, JSON, and Node native modules
  moduleFileExtensions: ['js', 'mjs', 'cjs', 'ts', 'mts', 'cts', 'json', 'node'],

  // Treat these extensions as ES modules
  // Ensures Jest correctly handles .ts files with ESM import/export syntax
  extensionsToTreatAsEsm: ['.ts'],

  // Transform TypeScript files using ts-jest with ESM enabled
  // This tells <PERSON><PERSON> how to compile TypeScript test files
  transform: {
    '^.+\\.ts$': ['ts-jest', { useESM: true }]
  },

  // Glob pattern to find test files
  // Jest will look for any .test.ts file inside the tests folder
  testMatch: ['**/tests/**/*.test.ts'],
};

// Export the config so Jest can use it
export default config;
