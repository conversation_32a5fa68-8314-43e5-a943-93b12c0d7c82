export default {
  arrowParens: 'avoid',       // Avoid parentheses around a sole arrow function parameter
  bracketSpacing: true,       // Print spaces between brackets in object literals
  singleQuote: true,          // Use single quotes for strings instead of double quotes
  trailingComma: 'all',       // Add trailing commas wherever possible (objects, arrays, function params)
  printWidth: 100,            // Wrap lines at 100 characters for readability
  tabWidth: 2,                // Use 2 spaces per indentation level

  // File-specific formatting rules
  overrides: [
    {
      // Apply these options only to JSON files
      files: '*.json',
      options: {
        printWidth: 200,       // Allow wider lines for JSON (no forced line breaks)
        singleQuote: false,   // JSON requires double quotes, so override singleQuote
      },
    },
    {
      // Apply these options only to Markdown files
      files: '*.md',
      options: {
        printWidth: 100,         // Wrap Markdown lines at 100 characters
        proseWrap: 'always',     // Always wrap prose (text paragraphs) to improve readability
      },
    },
  ],
};
