# Rishi Core

A TypeScript Node.js application with automated setup, build, and run scripts.

## 🚀 Quick Start

### Prerequisites

- **Node.js 22** (latest version) - Will be installed automatically via nvm
- **nvm** (Node Version Manager) - Will be installed automatically if not present
- **npm** (comes with Node.js)
- **Podman** - For database containerization. See [installation guide](https://podman.io/docs/installation).

### Setup

1. **Clone the repository** (if not already done):

```bash
git clone <repository-url>
cd rishi-core
```

2. **Check if you have podman installed**

Ensure you have podman installed in your development machine before running the setup script.
To verify that you have it installed, run [podman](https://podman.io/docs/installationhttps://podman.io/docs/installation) on your terminal to check if it's installed.

```bash
podman
```

3. **Run the setup script**:

```bash
chmod +x dev script/*.sh
./dev setup
```

This will:

- **Setup database containers using podman**

When you run the setup script for the first time, you will be prompted to create a password for the development database. **This is the password you will use to connect to the database from the application.**

- **Important Notes:**
  - If the script fails, make sure that port `5432` is not already in use.
  - If you have run the setup successfully before, the script will use the existing database container.
  - The script creates a database container named `rishiflow-db`.

The connection details for development are:

- **User:** `postgres`

- **Password:** The password you set during the initial setup.

- **Host:** `localhost`

- **Port:** `5432`

<br/>

- **Check Node.js and npm installation**

Ensures that `nvm` (Node version Manager) is installed
This checks if nodeJS is installed  and it's the required version. `(node 22+)`
If nvm and nodeJS is not installed, the script auto-downloads the two

- **Install all dependencies**

This ensures that `node_modules` are installed

- **Create necessary directories**

- **Build the project to verify everything works**

#### 

#### Using npm scripts:

```bash
# Development mode with hot reload
npm run dev

# Build for production
npm run build

# Run the built application
npm start

# Code quality and formatting
npm run lint          # Check for linting errors
npm run lint:fix      # Fix linting errors automatically
npm run format        # Format code with Prettier
npm run format:check  # Check if code is properly formatted
npm run check         # Run both lint and format check
npm run fix           # Fix both linting and formatting issues
```

#### Using convenience scripts:

```bash
# Using the dev helper (recommended)
./dev setup                  # Set up the development environment
./dev node                   # Switch to correct Node.js version
./dev run                    # Lint, build and run the project
./dev run --skip-lint        # Skip linting step
./dev run --lint-only        # Only run linting, don't build/run
./dev run --lint-fix         # Fix linting and formatting issues

# Or use scripts directly
script/setup.sh              # Set up the development environment
script/use-node.sh           # Switch to correct Node.js version
script/run.sh                # Lint, build and run the project
```

## Database

The project uses a PostgreSQL database managed by Podman. The database is automatically set up when you run the `dev setup` script. For more details on the database setup, users, and privileges, please see the [Database Setup Documentation](./docs/DATABASE_SETUP.md).


## 📁 Current Project Structure

```
rishi-core/

├── src/                 # TypeScript source files
│   ├── App.ts          # Main application entry point
│   ├── controllers/    # Route controllers
│   │   └── dataController.ts
│   └── routes/         # Express routes
│       └── routes.ts
├── config/             # Configuration files
│   └── index.ts        # Application configuration
├── docs/               # Documentation files
│   └── DATABASE_SETUP.md # Database setup documentation
├── script/             # Build and utility scripts
│   ├── setup.sh        # Environment setup script
│   ├── run.sh          # Build and run script
│   └── use-node.sh     # Node.js version switcher script
├── dist/               # Compiled JavaScript output (auto-generated)
├── logs/               # Application logs (auto-generated)
├── node_modules/       # Dependencies (auto-generated)
├── dev                 # Development helper script (convenience wrapper)

├── .nvmrc              # Node.js version lock file (auto-generated)
├── .env                # Environment variables (create this file)
├── .gitignore         # Git ignore rules
├── .prettierrc        # Prettier configuration
├── .prettierignore    # Prettier ignore rules
├── eslint.config.js   # ESLint configuration
├── package.json        # Project configuration and dependencies
├── package-lock.json   # Dependency lock file
├── tsconfig.json       # TypeScript configuration
└── README.md          # This file
```

## 🛠️ Scripts Documentation

### dev (Development Helper)

**Purpose**: Convenience wrapper for all development scripts.

**What it does**:

- Provides a simple interface to run setup and development scripts
- Shows helpful usage information
- Passes through all arguments to the underlying scripts

**Usage**:

```bash
chmod +x dev
./dev                    # Show help
./dev setup              # Run setup script
./dev node               # Switch to correct Node.js version
./dev run                # Run development script
./dev run --lint-fix     # Run with lint fix option
```

### script/use-node.sh (Node.js Version Helper)

**Purpose**: Ensures the correct Node.js version is being used.

**What it does**:

- Loads nvm (Node Version Manager)
- Switches to the Node.js version specified in `.nvmrc`
- Provides guidance for making the version change permanent

**Usage**:

```bash
chmod +x script/use-node.sh
script/use-node.sh       # Switch to project's Node.js version
# or
./dev node               # Same as above via dev helper
```

### script/setup.sh

**Purpose**: Sets up the development environment from scratch.

**What it does**:

- Verifies Node.js and npm installation
- Checks Node.js version compatibility
- Installs project dependencies
- Creates necessary directories
- Performs initial build to verify setup
- Provides helpful next steps

**Usage**:

```bash
chmod +x script/setup.sh
script/setup.sh
```

### script/run.sh

**Purpose**: Lints, builds and runs the application with various options.

**What it does**:

- Checks if dependencies are installed
- Runs ESLint to check code quality (unless skipped)
- Cleans previous build output
- Compiles TypeScript to JavaScript
- Verifies build success
- Shows output file information
- Runs the compiled application
- Provides clear output separation

**Usage**:

```bash
chmod +x script/run.sh
script/run.sh                # Full process: lint → build → run
script/run.sh --skip-lint    # Skip linting step
script/run.sh --lint-only    # Only run linting check
script/run.sh --lint-fix     # Fix linting and formatting issues
```

## 🔧 Configuration

### Configuration File (config.yaml)

A `config.yaml` file will be created in the root of the project when you run `./dev setup`. This file is ignored by Git and contains your environment-specific configurations for the database and server.

**Example:**

```yaml

MainDatabase:
  host: host_port
  port: database_exposed_port
  name: database_name

DatabaseOwner:
  user: owner_name
  password: owne_password

PostgresSuperuser:
  user: postgres
  password: postgres_password

ApplicationUser:
  user: application_user_name
  password: application_password

MigrationUser:
  user: migration_user_name
  password: migration_password

# Server Configuration
Server:
  port: server_port_here
  host: host_port_here

```

### TypeScript Configuration (tsconfig.json)

- **Target**: ESNext
- **Module**: NodeNext
- **Source Directory**: `src/`
- **Output Directory**: `dist/`
- **Source Maps**: Enabled for debugging

### Package Configuration (package.json)

- **Main Entry**: `App.js`
- **Scripts**: dev, build, start, lint, format
- **Dependencies**: TypeScript, ts-node, @types/node, Express
- **Dev Dependencies**: ESLint, Prettier, TypeScript ESLint plugins

## 🎯 Code Quality Tools

### ESLint Configuration

- **Parser**: @typescript-eslint/parser
- **Plugins**: @typescript-eslint, prettier
- **Rules**:
  - TypeScript-specific rules for better code quality
  - Prettier integration for consistent formatting
  - Custom rules for semicolons, quotes, and unused variables
- **Ignored**: dist/, node_modules/, *.js, *.mjs

### Prettier Configuration

- **Semi**: true (always use semicolons)
- **Single Quote**: true (prefer single quotes)
- **Trailing Comma**: all (add trailing commas)
- **Tab Width**: 2 spaces
- **Print Width**: 100 characters
- **Ignored**: dist/, node_modules/

## 📝 Development Workflow

1. **Initial Setup**:
   
   ```bash
   ./dev setup
   ```

2. **Switch to Correct Node.js Version** (for new terminal sessions):
   
   ```bash
   ./dev node       # Switch to Node.js 22
   ```

3. **Development**:
   
   ```bash
   npm run dev      # Hot reload development
   ```

4. **Code Quality Checks**:
   
   ```bash
   npm run check    # Check linting and formatting
   npm run fix      # Fix linting and formatting issues
   ```

5. **Testing Changes**:
   
   ```bash
   ./dev run        # Lint, build and run the application
   ```

## 🔧 Node.js Version Management

This project uses **Node.js 22** (the latest version) managed through **nvm** (Node Version Manager).

### Automatic Setup

The setup script automatically:

- Installs nvm if not present
- Installs Node.js 22
- Creates `.nvmrc` file to lock the version
- Configures the project to use the correct version

### Manual Version Switching

If you open a new terminal session, you may need to switch to the correct Node.js version:

```bash
# Using the dev helper (recommended)
./dev node

# Or directly with nvm
nvm use 22
# or simply: nvm use (reads from .nvmrc)
```

### Making Node.js Version Permanent

To automatically use the correct Node.js version in new terminal sessions, add this to your shell profile (`.bashrc`, `.zshrc`, etc.):

```bash
# Auto-switch Node.js version when entering project directory
cd() {
  builtin cd "$@"
  if [[ -f .nvmrc ]]; then
    nvm use
  fi
}
```

4. **Production Build**:
   
   ```bash
   npm run build
   npm start
   ```

## 🚫 What's Ignored (.gitignore)

The `.gitignore` file excludes:

- **Build outputs**: `dist/`, `build/`
- **Dependencies**: `node_modules/`
- **IDE files**: `.vscode/`, `.idea/`
- **OS files**: `.DS_Store`, `Thumbs.db`
- **Environment files**: `.env.*` (except `.env.example`)
- **Logs and cache**: `*.log`, `.cache`
- **Temporary files**: `*.tmp`, `*.temp`
- **Database files**: `*.db`, `*.sqlite`
- **Archive files**: `*.zip`, `*.tar.gz`

## 🐛 Troubleshooting

### Common Issues

1. **"Node.js is not installed"**
   
   - Install Node.js from [nodejs.org](https://nodejs.org/)
   - Restart your terminal after installation

2. **"Dependencies not found"**
   
   - Run `script/setup.sh` to install dependencies
   - Or manually run `npm install`

3. **"Linting failed"**
   
   - Check ESLint errors in the output
   - Run `script/run.sh --lint-fix` to auto-fix many issues
   - Run `npm run fix` to fix both linting and formatting

4. **"Build failed"**
   
   - Check TypeScript errors in the output
   - Verify all source files are valid TypeScript
   - Run `npm run dev` to see detailed error messages

5. **Permission denied on scripts**
   
   - Make scripts executable: `chmod +x dev script/*.sh`

### Getting Help

If you encounter issues:

1. Check the error messages carefully
2. Verify Node.js version: `node --version`
3. Try cleaning and rebuilding: `rm -rf dist node_modules && script/setup.sh`
