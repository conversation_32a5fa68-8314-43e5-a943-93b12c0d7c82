{
  "compilerOptions": {
    "target": "ES2020",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "rootDir": ".",
    "outDir": "./dist",
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "strict": true,
    "noImplicitAny": true,              //  recommended for clarity
    "noImplicitThis": true,             // recommended for clarity
    "noUnusedLocals": true,             // additional strictness
    "noUnusedParameters": true,         //  additional strictness
    "noFallthroughCasesInSwitch": true, // additional strictness
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src", "config"],
  "exclude": ["node_modules", "dist"]
}