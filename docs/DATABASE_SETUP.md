# Database Setup

This document outlines the database setup for the Rishi Core project, including the database schema, users, and their respective privileges.

## Database

*   **Database Name:** `rishiflow`

The primary database for the application is named `rishiflow`.

## Users and Privileges

The database is managed by a set of users, each with specific roles and privileges.

### Database Owner

*   **Username:** `rishiflow`
*   **Privileges:**
    *   Full ownership of the `rishiflow` database.
    *   `CREATEDB`: Can create new databases.
    *   `ALL PRIVILEGES`: Has all privileges on the `rishiflow` database, including creating, altering, and dropping tables and other database objects.
*   **Usage:** This user is intended for high-level database administration and ownership tasks.

### Application User

*   **Username:** `app_user`
*   **Privileges:**
    *   `CONNECT`: Can connect to the `rishiflow` database.
    *   `USAGE`: Has usage rights on the `public` schema.
    *   `SELECT`, `INSERT`, `UPDATE`, `DELETE`: Can perform standard data manipulation operations on all tables within the `public` schema.
*   **Usage:** This user is used by the application for its day-to-day database operations.

### Migration User

*   **Username:** `migration_user`
*   **Privileges:**
    *   `CONNECT`: Can connect to the `rishiflow` database.
    *   `USAGE`, `CREATE`: Has usage and creation rights on the `public` schema.
    *   `ALL PRIVILEGES`: Has all privileges on all tables and sequences in the `public` schema, allowing for schema modifications.
*   **Usage:** This user is designated for running database migrations, which may involve creating, altering, or dropping tables and other schema changes.

### PostgreSQL Superuser

*   **Username:** `postgres`
*   **Privileges:**
    *   Full administrative access to the entire PostgreSQL instance.
    *   Can create and drop databases and users.
*   **Usage:** This is the default superuser for PostgreSQL and should be used for system-level database administration.
