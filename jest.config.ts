// Import the type definition for Jest config (helps TypeScript catch mistakes)
import type { Config } from 'jest';

// Define the Jest configuration object
const config: Config = {
  // Use the ESM preset for ts-jest
  // This preset allows <PERSON><PERSON> to work with ES Modules (import/export) in TypeScript
  preset: 'ts-jest/presets/default-esm',

  // Set the test environment to Node.js
  // This is important for backend tests (Express, Supertest, etc.)
  testEnvironment: 'node',

  // File extensions Jest will look for when resolving modules
  // Includes TypeScript, JavaScript, JSON, and Node native modules
  moduleFileExtensions: ['js', 'mjs', 'cjs', 'ts', 'mts', 'cts', 'json', 'node'],

  // Treat these extensions as ES modules
  // Ensures Jest correctly handles .ts files with ESM import/export syntax
  extensionsToTreatAsEsm: ['.ts'],

  // Transform TypeScript files using ts-jest with ESM enabled
  // This tells <PERSON><PERSON> how to compile TypeScript test files
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        module: 'esnext'
      }
    }]
  },

  // Module name mapping to handle .js imports in TypeScript
  // This allows importing .ts files with .js extensions (TypeScript convention)
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1'
  },

  // Glob pattern to find test files
  // Jest will look for any .test.ts file inside the tests folder
  testMatch: ['**/tests/**/*.test.ts'],

  // Setup files to run before tests
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],

  // Set the root directory for tests
  rootDir: '.',

  // Test timeout (30 seconds)
  testTimeout: 30000,
};

// Export the config so Jest can use it
export default config;
