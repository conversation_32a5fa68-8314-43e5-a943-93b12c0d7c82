import { Pool } from 'pg';
import { CONFIG } from '../../config/index.js';
import logger from '../utils/logger.js';

// Pool is a type of connection in pg that can be reused efficiently
// PostgreSQL connection pool configuration using config.yaml
const POOL = new Pool({
  host: CONFIG.MainDatabase.host,
  port: CONFIG.MainDatabase.port,
  user: CONFIG.ApplicationUser.user,
  password: CONFIG.ApplicationUser.password,
  database: CONFIG.MainDatabase.name,
});

//Test connection
export const testConnection = async () => {
  let client;
  try {
    // Get a client from the pool to test the connection
    client = await POOL.connect();
    const result = await client.query('SELECT NOW()');
    logger.info('Database connection successful at:', result.rows[0].now);
  } catch (err) {
    logger.error(
      'Database connection error. Please check config.yaml and .env files and ensure the database container is running.',
    );
    throw err;
  } finally {
    // Ensure the client is released back to the pool, whether the query succeeded or failed
    client?.release();
  }
};
