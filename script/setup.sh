#!/bin/bash

# Rishi Core - Setup Script
# This script sets up the development environment for the Rishi Core project

set -e  # Exit on any error

# Database setup operation
echo "Database setup"
echo


CONTAINER_NAME="rishiflow-db"
HOST_DATA_DIR="$HOME/.rishiflow/db/postgresql"
HOST_PORT=5432

if [ -x "$(command -v podman)" ]; then

    if podman container exists "$CONTAINER_NAME"; then

        # Check if container is running
        if podman container inspect -f '{{.State.Running}}' "$CONTAINER_NAME" | grep -q "true"; then
            echo "Container '$CONTAINER_NAME' is already running."
        else
            echo "Starting existing container '$CONTAINER_NAME'..."
            podman start "$CONTAINER_NAME"
        fi
    else
        # Prompt for password
        read -sp "Enter a password to be used for your PostgreSQL database: " password
        echo

        # Ensure the data directory exists
        if [ ! -d "$HOST_DATA_DIR" ]; then
            echo "Creating data directory at $HOST_DATA_DIR..."
            mkdir -p "$HOST_DATA_DIR"
        fi

        # Run new container
        podman run -dt --name "$CONTAINER_NAME" \
            -e POSTGRES_PASSWORD="$password" \
            -v "$HOST_DATA_DIR:/var/lib/postgresql/data" \
            -p "$HOST_PORT:5432" \
            docker.io/library/postgres:17
        echo "Container '$CONTAINER_NAME' has been created and started."
    fi

else
    echo "Please ensure that you have installed podman in your system"
    echo "Please visit https://podman.io/docs/installation to check installation procedures for your system."
    exit 1;
fi

# Database and User Setup
echo ""
echo "🗄️  Setting up database and users..."

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..15}; do
    if podman exec "$CONTAINER_NAME" pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi

    if [ $i -eq 15 ]; then
        echo "❌ PostgreSQL failed to start after 15 attempts"
        exit 1
    fi

    echo "   Attempt $i/15: PostgreSQL not ready yet, waiting 2 seconds..."
    sleep 2
done

# Database configuration
DB_NAME="rishiflow"
APP_USER="app_user"
MIGRATION_USER="migration_user"

# Generate random passwords for users
APP_USER_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
MIGRATION_USER_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)

echo ""
echo "📊 Creating database '$DB_NAME'..."

# Create database with retry logic
for i in {1..15}; do
    if podman exec "$CONTAINER_NAME" psql -U postgres -c "CREATE DATABASE $DB_NAME;" > /dev/null 2>&1; then
        echo "✅ Database '$DB_NAME' created successfully!"
        break
    fi

    # Check if database already exists
    if podman exec "$CONTAINER_NAME" psql -U postgres -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        echo "✅ Database '$DB_NAME' already exists!"
        break
    fi

    if [ $i -eq 15 ]; then
        echo "❌ Failed to create database after 15 attempts"
        exit 1
    fi

    echo "   Attempt $i/15: Database creation failed, retrying in 1 second..."
    sleep 1
done

echo ""
echo "👤 Creating database users and setting up privileges..."

# Create database owner (postgres user already exists and owns the database)
echo "✅ Database owner: postgres (default superuser)"

# Create app_user with data manipulation privileges
echo "📝 Setting up app_user with data manipulation privileges..."

# Check if app_user exists, create if not
if podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -tAc "SELECT 1 FROM pg_roles WHERE rolname='$APP_USER'" | grep -q 1; then
    echo "   User '$APP_USER' already exists, updating password and privileges..."
    podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "ALTER USER $APP_USER WITH PASSWORD '$APP_USER_PASSWORD';"
else
    echo "   Creating new user '$APP_USER'..."
    podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "CREATE USER $APP_USER WITH PASSWORD '$APP_USER_PASSWORD';"
fi

# Grant/update privileges for app_user
podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "
-- Grant connection to database
GRANT CONNECT ON DATABASE $DB_NAME TO $APP_USER;

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO $APP_USER;

-- Grant data manipulation privileges on all existing tables
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO $APP_USER;

-- Grant data manipulation privileges on all future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO $APP_USER;

-- Grant usage on all sequences (for auto-increment columns)
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO $APP_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE ON SEQUENCES TO $APP_USER;
"

echo "✅ app_user configured with data manipulation privileges"

# Create migration_user with schema modification privileges
echo "📝 Setting up migration_user with schema modification privileges..."

# Check if migration_user exists, create if not
if podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -tAc "SELECT 1 FROM pg_roles WHERE rolname='$MIGRATION_USER'" | grep -q 1; then
    echo "   User '$MIGRATION_USER' already exists, updating password and privileges..."
    podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "ALTER USER $MIGRATION_USER WITH PASSWORD '$MIGRATION_USER_PASSWORD';"
else
    echo "   Creating new user '$MIGRATION_USER'..."
    podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "CREATE USER $MIGRATION_USER WITH PASSWORD '$MIGRATION_USER_PASSWORD';"
fi

# Grant/update privileges for migration_user
podman exec "$CONTAINER_NAME" psql -U postgres -d "$DB_NAME" -c "
-- Grant connection to database
GRANT CONNECT ON DATABASE $DB_NAME TO $MIGRATION_USER;

-- Grant usage and create on schema
GRANT USAGE, CREATE ON SCHEMA public TO $MIGRATION_USER;

-- Grant schema modification privileges on all existing tables
GRANT CREATE ON SCHEMA public TO $MIGRATION_USER;

-- Grant all privileges on all existing tables (needed for ALTER/DROP)
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $MIGRATION_USER;

-- Grant all privileges on all future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO $MIGRATION_USER;

-- Grant all privileges on sequences
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $MIGRATION_USER;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO $MIGRATION_USER;
"

echo "✅ migration_user configured with schema modification privileges"

# Save connection details to a file
echo ""
echo "💾 Saving database connection details..."
cat > .env.database << EOF
# Database Connection Details
# Generated by setup.sh on $(date)

# Main Database
DB_HOST=localhost
DB_PORT=$HOST_PORT
DB_NAME=$DB_NAME

# Database Owner (Superuser)
DB_OWNER_USER=postgres
DB_OWNER_PASSWORD=$password

# Application User (Data Operations: SELECT, INSERT, UPDATE, DELETE)
DB_APP_USER=$APP_USER
DB_APP_PASSWORD=$APP_USER_PASSWORD

# Migration User (Schema Operations: CREATE, ALTER, DROP tables)
DB_MIGRATION_USER=$MIGRATION_USER
DB_MIGRATION_PASSWORD=$MIGRATION_USER_PASSWORD
EOF

echo "✅ Database connection details saved to .env.database"

echo ""
echo "📋 Database Setup Summary:"
echo "  Database: $DB_NAME"
echo "  Host: localhost:$HOST_PORT"
echo ""
echo "👥 Database Users and Privileges:"
echo ""
echo "  🔑 postgres (Database Owner/Superuser):"
echo "    - Full administrative access to all databases"
echo "    - Can create/drop databases and users"
echo "    - Use for: Database administration tasks"
echo ""
echo "  👤 $APP_USER (Application User):"
echo "    - Privileges: SELECT, INSERT, UPDATE, DELETE"
echo "    - Scope: All tables in public schema (current and future)"
echo "    - Use for: Application runtime database operations"
echo ""
echo "  🔧 $MIGRATION_USER (Migration User):"
echo "    - Privileges: CREATE, ALTER, DROP tables"
echo "    - Scope: Full schema modification rights in public schema"
echo "    - Use for: Database migrations and schema changes"
echo ""
echo "⚠️  Security Notes:"
echo "  - User passwords are randomly generated and saved in .env.database"
echo "  - Keep .env.database secure and do not commit to version control"
echo "  - Consider adding .env.database to your .gitignore file"
echo ""



# Change to project root directory (parent of script directory)
cd "$(dirname "$0")/.."

echo "🚀 Setting up Rishi Core development environment..."

# Node.js version to install
NODE_VERSION="22"
REQUIRED_VERSION="22.0.0"

# Check if nvm is installed
if ! command -v nvm &> /dev/null; then
    echo "📦 nvm not found. Installing nvm..."

    # Download and install nvm
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

    # Source nvm script to make it available in current session
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

    if ! command -v nvm &> /dev/null; then
        echo "❌ Failed to install nvm. Please install it manually:"
        echo "   Visit: https://github.com/nvm-sh/nvm#installing-and-updating"
        exit 1
    fi

    echo "✅ nvm installed successfully"
else
    echo "✅ nvm is already installed"

    # Source nvm to ensure it's available
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
fi

# Install and use Node.js 22
echo "📦 Installing Node.js $NODE_VERSION (latest)..."
nvm install $NODE_VERSION
nvm use $NODE_VERSION

# Verify Node.js installation
if ! command -v node &> /dev/null; then
    echo "❌ Node.js installation failed"
    exit 1
fi

# Check Node.js version
CURRENT_NODE_VERSION=$(node --version | cut -d'v' -f2)
echo "✅ Node.js v$CURRENT_NODE_VERSION is now active"

# Verify we have the correct major version
MAJOR_VERSION=$(echo $CURRENT_NODE_VERSION | cut -d'.' -f1)
if [ "$MAJOR_VERSION" != "$NODE_VERSION" ]; then
    echo "⚠️  Expected Node.js $NODE_VERSION but got $MAJOR_VERSION"
    echo "   Attempting to switch to Node.js $NODE_VERSION..."
    nvm use $NODE_VERSION
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not available. This should come with Node.js."
    exit 1
fi

echo "✅ Node.js $(node --version) and npm $(npm --version) are available"

# Create .nvmrc file to lock Node.js version for the project
echo "📝 Creating .nvmrc file to lock Node.js version..."
echo "$NODE_VERSION" > .nvmrc
echo "✅ Created .nvmrc with Node.js $NODE_VERSION"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Verify TypeScript installation
if ! npx tsc --version &> /dev/null; then
    echo "❌ TypeScript installation failed"
    exit 1
fi

echo "✅ TypeScript $(npx tsc --version) is available"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p dist
mkdir -p logs

# Build the project to verify everything works
echo "🔨 Building project to verify setup..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Node.js Environment:"
echo "  Node.js: $(node --version)"
echo "  npm: $(npm --version)"
echo "  nvm: $(nvm --version)"
echo ""
echo "💡 To use this Node.js version in new terminal sessions:"
echo "  nvm use 22"
echo "  # or simply: nvm use (reads from .nvmrc)"
echo ""
echo "Available commands:"
echo "  npm run dev     - Run in development mode with hot reload"
echo "  npm run build   - Build the project for production"
echo "  npm run start   - Run the built project"
echo "  npm run lint    - Check code quality with ESLint"
echo "  npm run fix     - Fix linting and formatting issues"
echo ""
echo "You can also use the convenience script:"
echo "  script/run.sh        - Lint, build and run the project"
echo "  script/run.sh --lint-fix - Fix code quality issues"
echo ""
echo "Happy coding! 🚀"
