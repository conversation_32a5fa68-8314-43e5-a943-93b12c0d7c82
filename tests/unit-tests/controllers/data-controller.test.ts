import { helloHand<PERSON> } from '../../../src/controllers/data-controller.js';
import { Request, Response } from 'express';

describe('Data Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let sendMock: jest.Mock;

  beforeEach(() => {
    sendMock = jest.fn();
    req = {};
    res = {
      send: sendMock,
    };
  });

  test('helloHandler should send a "Hello, Express + TypeScript!" message', () => {
    helloHandler(req as Request, res as Response);
    expect(sendMock).toHaveBeenCalledWith('Hello, Express + TypeScript!');
  });
});
