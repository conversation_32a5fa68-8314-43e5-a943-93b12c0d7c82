import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import 'dotenv/config';
import { AppConfig, YamlConfig } from '../src/types/config.js';
import logger from '../src/utils/logger.js';

// Paths to configuration files
const CONFIG_PATH = path.join(process.cwd(), 'config.yaml');

const getRequiredEnv = (key: string): string => {
  const value = process.env[key];
  if (!value) {
    logger.error(`❌ Required environment variable ${key} is not set. The application will exit.`);
    process.exit(1);
  }
  return value;
};

const getConfigValue = (
  envKey: string,
  yamlValue: string | undefined,
  defaultValue: string,
): string => {
  return process.env[envKey] ?? yamlValue ?? defaultValue;
};

// Read and parse the YAML configuration file
let yamlConfig: YamlConfig = {};
try {
  if (fs.existsSync(CONFIG_PATH)) {
    const fileContents = fs.readFileSync(CONFIG_PATH, 'utf-8');
    yamlConfig = yaml.load(fileContents) as YamlConfig;
  }
} catch (error: unknown) {
  if (error instanceof Error) {
    logger.error(`❌ Failed to read or parse config.yaml: ${error.message}`);
  } else {
    logger.error(`❌ Failed to read or parse config.yaml:${error}`);
  }
  process.exit(1);
}

// Build configuration object combining YAML structure with .env sensitive data
const CONFIG: AppConfig = {
  MainDatabase: {
    host: getConfigValue('DB_HOST', yamlConfig.MainDatabase?.host, 'localhost'),
    port: parseInt(getConfigValue('DB_PORT', yamlConfig.MainDatabase?.host, '5432'), 10),
    name: getConfigValue('DB_NAME', yamlConfig.MainDatabase?.name, 'rishiflow'),
  },
  DatabaseOwner: {
    user: getConfigValue('DB_OWNER_USER', yamlConfig.DatabaseOwner?.user, 'rishiflow'),
    password: getRequiredEnv('DB_OWNER_PASSWORD'),
  },
  PostgresSuperuser: {
    user: getConfigValue('DB_POSTGRES_USER', yamlConfig.PostgresSuperuser?.user, 'postgres'),
    password: getRequiredEnv('DB_POSTGRES_PASSWORD'),
  },
  ApplicationUser: {
    user: getConfigValue('DB_APP_USER', yamlConfig.ApplicationUser?.user, 'app_user'),
    password: getRequiredEnv('DB_APP_PASSWORD'),
  },
  MigrationUser: {
    user: getConfigValue('DB_MIGRATION_USER', yamlConfig.MigrationUser?.user, 'migration_user'),
    password: getRequiredEnv('DB_MIGRATION_PASSWORD'),
  },
  Server: {
    port: parseInt(getRequiredEnv('SERVER_PORT'), 10),
    host: getConfigValue('SERVER_HOST', yamlConfig.Server?.host, '127.0.0.1'),
  },
};

export { CONFIG, CONFIG_PATH };
