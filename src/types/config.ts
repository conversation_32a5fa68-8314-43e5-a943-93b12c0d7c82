//interface which create a blue print for objects
//the load data from config.yaml will be pasted into the objects (the object implement the interfaces)
export interface AppConfig {
  MainDatabase: {
    host: string;
    port: number;
    name: string;
  };
  DatabaseOwner: {
    user: string;
    password: string;
  };
  PostgresSuperuser: {
    user: string;
    password: string;
  };
  ApplicationUser: {
    user: string;
    password: string;
  };
  MigrationUser: {
    user: string;
    password: string;
  };
  Server: {
    port: number;
    host: string;
  };
}
