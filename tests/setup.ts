// Test setup file - runs before all tests
// This file sets up the test environment with necessary configurations

import fs from 'fs';
import path from 'path';

// Set up environment variables for testing
process.env.NODE_ENV = 'test';

// Copy test config.yaml to root directory for tests
const testConfigPath = path.join(__dirname, 'config.yaml');
const rootConfigPath = path.join(process.cwd(), 'config.yaml');

// Backup existing config if it exists
let originalConfigBackup: string | null = null;
if (fs.existsSync(rootConfigPath)) {
  originalConfigBackup = fs.readFileSync(rootConfigPath, 'utf-8');
}

// Copy test config
if (fs.existsSync(testConfigPath)) {
  fs.copyFileSync(testConfigPath, rootConfigPath);
}

// Cleanup function to restore original config after tests
const cleanup = () => {
  if (originalConfigBackup) {
    fs.writeFileSync(rootConfigPath, originalConfigBackup);
  } else if (fs.existsSync(rootConfigPath)) {
    // Only remove if we created it and there was no original
    const currentContent = fs.readFileSync(rootConfigPath, 'utf-8');
    const testContent = fs.readFileSync(testConfigPath, 'utf-8');
    if (currentContent === testContent) {
      fs.unlinkSync(rootConfigPath);
    }
  }
};

// Register cleanup
process.on('exit', cleanup);
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// Database configuration for tests
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_NAME = 'rishiflow_test';
process.env.DB_OWNER_USER = 'rishiflow';
process.env.DB_OWNER_PASSWORD = 'test_password';
process.env.DB_POSTGRES_USER = 'postgres';
process.env.DB_POSTGRES_PASSWORD = 'test_password';
process.env.DB_APP_USER = 'app_user';
process.env.DB_APP_PASSWORD = 'test_password';
process.env.DB_MIGRATION_USER = 'migration_user';
process.env.DB_MIGRATION_PASSWORD = 'test_password';

// Server configuration for tests
process.env.SERVER_PORT = '3001';
process.env.SERVER_HOST = '127.0.0.1';

// Mock console methods to reduce noise during tests (optional)
// Uncomment if you want to suppress console output during tests
// global.console = {
//   ...console,
//   log: jest.fn(),
//   debug: jest.fn(),
//   info: jest.fn(),
//   warn: jest.fn(),
//   error: jest.fn(),
// };
