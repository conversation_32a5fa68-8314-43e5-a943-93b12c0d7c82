import { createApp } from './app.js';
import { CONFIG } from '../config/index.js';
import { testConnection } from './database/db-connection.js';
import logger from './utils/logger.js';

const startServer = async () => {
  try {
    // Verify database connectivity before starting the server
    await testConnection();

    // Initialize Express application
    const app = createApp();
    const { port, host } = CONFIG.Server;

    // Start the server
    const server = app.listen(port, host, () => {
      logger.info(`🚀 Server running at http://${host}:${port}`);
    });

    // Handle server-level errors (e.g. port already in use)
    server.on('error', error => {
      logger.error(`Server error: ${error}`);
      process.exit(1);
    });
  } catch (error) {
    // Log and exit if startup fails
    logger.error(`❌ Failed to start server. Please check the logs above for details ${error}`);
    process.exit(1);
  }
};

startServer();
