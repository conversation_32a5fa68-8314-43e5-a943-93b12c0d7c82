{"name": "rishi-core", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "tsx watch src/server.ts", "build": "npm run typecheck && tsc", "typecheck": "tsc --noEmit", "start": "node dist/src/server.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "check": "npm run typecheck && npm run lint && npm run format:check", "fix": "npm run lint:fix && npm run format"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/express": "^5.0.3", "@types/js-yaml": "^4.0.9", "@types/node": "^24.3.0", "@types/pg": "^8.15.5", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "prettier": "^3.6.2", "ts-node": "^10.9.2", "tsx": "^4.20.4", "typescript": "^5.9.2"}, "dependencies": {"dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "js-yaml": "^4.1.0", "pg": "^8.16.3"}}