## 📌 Description
**Summary of Changes:**  
Please provide a brief, high-level overview of the changes in this pull request.

**Related Issue:**  
Fixes # (issue number or link to issue)

**Motivation and Context:**  
Explain the problem this PR solves and why this change is needed.

---

## 🛠 Type of Change
- [ ] 🐞 Bug fix (non-breaking change that fixes an issue)  
- [ ] ✨ New feature (non-breaking change that adds functionality)  
- [ ] 💥 Breaking change (a fix or feature that would cause existing functionality to not work as expected)  
- [ ] 📚 Documentation update  

---

## 🧪 How Has This Been Tested?
Please describe the tests you ran to verify your changes. Provide instructions so we can reproduce.

**Test Environment:**  
(e.g., local server, staging environment)

**Test Steps:**  
1. Step 1  
2. Step 2  
3. Step 3  

---

## ⚙️ Backend Specific (Node.js)
- [ ] API endpoints follow RESTful conventions.  
- [ ] Database schema changes are well-documented and migrations are reversible.  
- [ ] New and existing unit/integration tests pass.  
- [ ] API documentation (e.g., Swagger/OpenAPI) has been updated.  
- [ ] Considered query optimization for database interactions.  
- [ ] Background jobs are configured correctly (if applicable).  

---

## ✅ Checklist
- [ ] My code follows the style guidelines of this project (e.g., ESLint).  
- [ ] I have performed a self-review of my code.
- [ ] I have reviewed my code with AI and addressed important issues.
- [ ] I have commented my code, particularly in hard-to-understand areas.  
- [ ] I have made corresponding changes to the documentation.  
- [ ] My changes generate no new warnings.  
- [ ] I have added tests that prove my fix is effective or that my feature works.  
- [ ] New and existing unit tests pass locally with my changes.  
- [ ] I am making the PR to the **main** branch.  

---

## 🔒 Security Considerations
- [ ] Proper parameter sanitization is in place to prevent injection attacks.  
- [ ] Authentication and authorization checks are correctly implemented for API endpoints.  
- [ ] No sensitive data is exposed in logs or responses.  
- [ ] CSRF protection is maintained (for web-based APIs).  

---

## 🔍 Reviewer Notes
- Anything you’d like reviewers to focus on?  

---

## 📸 Screenshots / Logs (if applicable)
