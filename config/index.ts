import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import { AppConfig } from '../src/types/config.js';

// Path to config.yaml (project root is determined via process.cwd())
const CONFIG_PATH = path.join(process.cwd(), 'config.yaml');

// Read the YAML file as a UTF-8 string
const fileContents = fs.readFileSync(CONFIG_PATH, 'utf-8');

// Parse YAML into a JavaScript object and assert it matches AppConfig type
const CONFIG = yaml.load(fileContents) as AppConfig;

export { CONFIG, CONFIG_PATH };
